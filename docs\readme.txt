================================================================================
                        HỆ THỐNG QUẢN LÝ SINH VIÊN
                              (Student Management System)
================================================================================

TỔNG QUAN DỰ ÁN
===============
Hệ thống Quản lý Sinh viên là một ứng dụng web được phát triển bằng Django Framework,
hỗ trợ quản lý thông tin sinh viên, môn học, đăng ký học và tích hợp chatbot thông minh
sử dụng PhoBERT để hỗ trợ người dùng bằng tiếng Việt.

TÍNH NĂNG CHÍNH
===============

1. QUẢN LÝ SINH VIÊN
   - Th<PERSON><PERSON>, sử<PERSON>, x<PERSON>a thông tin sinh viên
   - <PERSON><PERSON><PERSON>n lý hồ sơ cá nhân (ảnh, thông tin liên hệ)
   - Theo dõi trạng thái học tập
   - Tìm kiếm và lọc sinh viên

2. QUẢN LÝ MÔN HỌC
   - Tạo và quản lý môn học theo học kỳ
   - Thiết lập thông tin môn học (mã môn, tín chỉ, mô tả)
   - Quản lý lịch học và thời gian

3. ĐĂNG KÝ HỌC VÀ ĐIỂM SỐ
   - Đăng ký môn học cho sinh viên
   - Nhập và quản lý điểm số
   - Tính toán GPA và thống kê học tập
   - Theo dõi tiến độ học tập

4. CHATBOT THÔNG MINH
   - Hỗ trợ tiếng Việt với PhoBERT
   - Trả lời câu hỏi về thông tin sinh viên, môn học
   - Tìm kiếm thông tin đăng ký học và điểm số
   - Hệ thống FAQ tự động
   - Phân quyền truy cập theo vai trò người dùng

5. BÁO CÁO VÀ THỐNG KÊ
   - Dashboard tổng quan
   - Báo cáo học tập
   - Thống kê sinh viên và môn học

CẤU TRÚC DỰ ÁN
===============

QuanLySinhVien/
├── chatbot/                    # Ứng dụng chatbot
│   ├── models.py              # Models: FAQ, ChatSession, ChatHistory
│   ├── services.py            # Logic xử lý chatbot và PhoBERT
│   ├── views.py               # API endpoints cho chatbot
│   └── training_data.py       # Dữ liệu huấn luyện
├── courses/                   # Ứng dụng quản lý môn học
│   ├── models.py              # Models: Course, Enrollment
│   ├── views.py               # Views quản lý môn học
│   └── admin.py               # Cấu hình Django Admin
├── students/                  # Ứng dụng quản lý sinh viên
│   ├── models.py              # Model: Student
│   ├── views.py               # Views quản lý sinh viên
│   └── admin.py               # Cấu hình Django Admin
├── quanlysinhvien/           # Cấu hình chính của dự án
│   ├── settings.py            # Cài đặt Django
│   ├── urls.py                # URL routing chính
│   └── views.py               # Views chung (dashboard, reports)
├── templates/                 # Templates HTML
├── static/                    # Files tĩnh (CSS, JS, images)
├── media/                     # Files upload (ảnh sinh viên)
├── docs/                      # Tài liệu dự án
└── logs/                      # Log files

CÔNG NGHỆ SỬ DỤNG
=================

Backend:
- Django 4.x (Python Web Framework)
- SQLite Database (có thể chuyển sang PostgreSQL/MySQL)
- PhoBERT (Vietnamese BERT model) cho NLP

Frontend:
- Bootstrap 5 (Responsive UI Framework)
- jQuery (JavaScript Library)
- HTML5/CSS3

Chatbot:
- Natural Language Processing với PhoBERT
- Intent Recognition và Entity Extraction
- Context-aware conversation
- FAQ System với similarity matching

CÀI ĐẶT VÀ CHẠY DỰ ÁN
======================

1. Yêu cầu hệ thống:
   - Python 3.8+
   - pip (Python package manager)

2. Cài đặt dependencies:
   pip install -r requirements.txt

3. Thiết lập database:
   python manage.py makemigrations
   python manage.py migrate

4. Tạo superuser:
   python manage.py createsuperuser

5. Chạy server:
   python manage.py runserver

6. Truy cập ứng dụng:
   - Trang chính: http://127.0.0.1:8000/
   - Admin panel: http://127.0.0.1:8000/admin/

CẤU HÌNH CHATBOT
================

Chatbot được cấu hình trong settings.py:

CHATBOT_CONFIG = {
    'INTENT_THRESHOLD': 0.3,           # Ngưỡng nhận diện ý định
    'MAX_RESPONSE_LENGTH': 2000,       # Độ dài tối đa phản hồi
    'RATE_LIMIT_PER_MINUTE': 60,       # Giới hạn số tin nhắn/phút
    'LOG_CONVERSATIONS': True,          # Lưu lịch sử chat
    'ENABLE_ANALYTICS': True,           # Bật phân tích
    'SESSION_TIMEOUT': 3600,            # Timeout phiên chat (giây)
}

PHÂN QUYỀN NGƯỜI DÙNG
====================

1. ADMIN/STAFF:
   - Truy cập đầy đủ tất cả chức năng
   - Quản lý sinh viên, môn học, điểm số
   - Xem thông tin của tất cả sinh viên
   - Truy cập Django Admin panel

2. SINH VIÊN:
   - Xem thông tin cá nhân
   - Xem môn học đã đăng ký
   - Xem điểm số của bản thân
   - Sử dụng chatbot với quyền hạn chế

TÍNH NĂNG CHATBOT
=================

Chatbot hỗ trợ các loại câu hỏi:

1. Thông tin sinh viên:
   - "Thông tin sinh viên của tôi"
   - "Tìm sinh viên có mã SV001"
   - "Danh sách tất cả sinh viên"

2. Thông tin môn học:
   - "Môn học IT001"
   - "Danh sách môn học học kỳ 1"
   - "Môn học có bao nhiêu tín chỉ"

3. Đăng ký học:
   - "Môn học tôi đã đăng ký"
   - "Đăng ký môn IT001"
   - "Hủy đăng ký môn học"

4. Điểm số:
   - "Điểm số của tôi"
   - "Điểm môn IT001"
   - "GPA của tôi"

5. Hỗ trợ chung:
   - "Hướng dẫn sử dụng"
   - "Câu hỏi thường gặp"

LOGS VÀ MONITORING
==================

Hệ thống ghi log các hoạt động:
- Chatbot conversations: logs/chatbot.log
- Django debug logs
- User activity tracking
- Performance monitoring

BẢO MẬT
=======

- Session-based authentication
- CSRF protection
- XSS filtering
- Role-based access control
- Secure file upload handling

PHÁT TRIỂN VÀ MỞ RỘNG
====================

Để mở rộng hệ thống:

1. Thêm models mới trong các app tương ứng
2. Cập nhật chatbot training data
3. Thêm templates và static files
4. Cấu hình URL routing
5. Viết tests cho các tính năng mới

LIÊN HỆ VÀ HỖ TRỢ
==================

Để được hỗ trợ kỹ thuật hoặc báo lỗi, vui lòng:
- Kiểm tra logs trong thư mục logs/
- Xem Django debug information
- Liên hệ team phát triển

================================================================================
                            Cập nhật lần cuối: 2025-07-14
================================================================================
