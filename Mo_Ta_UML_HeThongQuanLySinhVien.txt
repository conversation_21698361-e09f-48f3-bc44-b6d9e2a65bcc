================================================================================
                    MÔ TẢ CÁC BIỂU ĐỒ UML CHO HỆ THỐNG QUẢN LÝ SINH VIÊN
================================================================================

TỔNG QUAN HỆ THỐNG:
Hệ thống Quản lý Sinh viên là một ứng dụng web Django được xây dựng để quản lý thông tin 
sinh viên, môn học, đăng ký học và tích hợp chatbot hỗ trợ người dùng. Hệ thống sử dụng 
PhoBERT (Vietnamese BERT) cho xử lý ngôn ngữ tự nhiên trong chatbot.

================================================================================
1. BIỂU ĐỒ LỚP (CLASS DIAGRAM)
================================================================================

1.1. PACKAGE: AUTHENTICATION & AUTHORIZATION
--------------------------------------------

Class: User (Django Built-in)
- Thuộc tính:
  + id: Integer (Primary Key)
  + username: String (Unique, max 150 ký tự)
  + password: String (Hashed, max 128 ký tự)
  + email: String (max 254 ký tự)
  + first_name: String (max 150 ký tự)
  + last_name: String (max 150 ký tự)
  + is_staff: Boolean (Quyền truy cập admin)
  + is_active: Boolean (Tài khoản hoạt động)
  + is_superuser: Boolean (Quyền siêu quản trị)
  + date_joined: DateTime (Ngày tạo tài khoản)
  + last_login: DateTime (Lần đăng nhập cuối)

- Phương thức:
  + authenticate(): Boolean
  + check_password(password: String): Boolean
  + set_password(password: String): Void
  + get_full_name(): String

Class: Group (Django Built-in)
- Thuộc tính:
  + id: Integer (Primary Key)
  + name: String (Unique, max 150 ký tự)

- Quan hệ:
  + User ←→ Group (Many-to-Many qua UserGroups)

1.2. PACKAGE: STUDENTS
----------------------

Class: Student
- Thuộc tính:
  + id: Integer (Primary Key)
  + student_id: String (Unique, max 20 ký tự, chỉ chữ và số)
  + first_name: String (max 50 ký tự)
  + last_name: String (max 50 ký tự)
  + date_of_birth: Date
  + gender: String (Choices: 'M'=Nam, 'F'=Nữ, 'O'=Khác)
  + email: String (Unique, Email format)
  + phone_number: String (max 15 ký tự, regex validation)
  + address: Text (Optional)
  + enrollment_date: Date (Auto-generated)
  + photo: ImageField (Optional, upload to 'student_photos/')
  + is_active: Boolean (Default: True)

- Phương thức:
  + full_name(): String (Trả về họ tên đầy đủ)
  + __str__(): String (Format: "Họ Tên (Mã SV)")
  + get_enrollments(): QuerySet[Enrollment]
  + calculate_gpa(): Decimal

- Validation:
  + student_id: Regex ^[A-Za-z0-9]+$ (Không ký tự đặc biệt)
  + phone_number: Regex ^\+?1?\d{9,15}$ (Định dạng số điện thoại)

1.3. PACKAGE: COURSES
---------------------

Class: Course
- Thuộc tính:
  + id: Integer (Primary Key)
  + course_code: String (Unique, max 10 ký tự)
  + name: String (max 100 ký tự)
  + description: Text (Optional)
  + credits: PositiveSmallInteger (Số tín chỉ)
  + semester: String (Choices: '1'=HK1, '2'=HK2, '3'=HK Hè)
  + year: PositiveSmallInteger (Năm học)
  + start_date: Date (Ngày bắt đầu)
  + end_date: Date (Ngày kết thúc)
  + is_active: Boolean (Default: True)

- Phương thức:
  + __str__(): String (Format: "Tên môn (Mã môn)")
  + get_enrollments(): QuerySet[Enrollment]
  + get_student_count(): Integer
  + get_average_grade(): Decimal

Class: Enrollment (Association Class)
- Thuộc tính:
  + id: Integer (Primary Key)
  + student: ForeignKey(Student) (CASCADE)
  + course: ForeignKey(Course) (CASCADE)
  + enrollment_date: Date (Auto-generated)
  + grade: String (Optional, Choices: A+, A, B+, B, C+, C, D+, D, F)
  + numeric_grade: Decimal (Optional, max_digits=4, decimal_places=2)

- Phương thức:
  + __str__(): String (Format: "Sinh viên - Môn học")
  + is_passed(): Boolean (Kiểm tra đã qua môn)

- Constraints:
  + unique_together: ['student', 'course'] (Một SV chỉ đăng ký 1 lần/môn)

1.4. PACKAGE: CHATBOT
---------------------

Class: FAQ
- Thuộc tính:
  + id: Integer (Primary Key)
  + question: Text (Câu hỏi)
  + answer: Text (Câu trả lời)
  + category: String (Choices: general, student, course, enrollment, grade, system)
  + keywords: Text (Optional, từ khóa phân cách bằng dấu phẩy)
  + is_active: Boolean (Default: True)
  + created_at: DateTime (Auto-generated)
  + updated_at: DateTime (Auto-updated)

- Phương thức:
  + __str__(): String
  + get_keywords_list(): List[String]
  + search_by_keywords(keywords: String): QuerySet[FAQ]

Class: ChatSession
- Thuộc tính:
  + id: Integer (Primary Key)
  + user: ForeignKey(User) (CASCADE)
  + session_id: String (Unique, max 100 ký tự)
  + started_at: DateTime (Auto-generated)
  + last_activity: DateTime (Auto-updated)
  + is_active: Boolean (Default: True)

- Phương thức:
  + __str__(): String (Format: "Chat username - session_id[:8]")
  + get_messages(): QuerySet[ChatHistory]
  + close_session(): Void

Class: ChatHistory
- Thuộc tính:
  + id: Integer (Primary Key)
  + session: ForeignKey(ChatSession) (CASCADE)
  + message_type: String (Choices: 'user', 'bot')
  + message: Text (Nội dung tin nhắn)
  + intent: String (Optional, max 50 ký tự, ý định được phân tích)
  + confidence: Float (Optional, độ tin cậy của phân tích)
  + response_time: Float (Optional, thời gian phản hồi tính bằng giây)
  + timestamp: DateTime (Auto-generated)

- Phương thức:
  + __str__(): String (Format: "username - message_type - timestamp")

Class: ChatbotService (Service Layer)
- Thuộc tính:
  + logger: Logger
  + intent_patterns: Dict (Patterns để nhận diện ý định)

- Phương thức:
  + process_message(user: User, message: String): Dict
  + analyze_intent(message: String): Dict
  + search_faq(query: String): List[FAQ]
  + get_student_info(user: User, query: String): String
  + get_course_info(query: String): String
  + get_enrollment_info(user: User, query: String): String
  + normalize_text(text: String): String
  + calculate_similarity(text1: String, text2: String): Float

1.5. QUAN HỆ GIỮA CÁC LỚP
--------------------------

Quan hệ chính:
1. User ←→ Group (Many-to-Many)
2. Student ←→ Course (Many-to-Many qua Enrollment)
3. User → ChatSession (One-to-Many)
4. ChatSession → ChatHistory (One-to-Many)
5. Student → Enrollment (One-to-Many)
6. Course → Enrollment (One-to-Many)

Quan hệ phụ thuộc:
- ChatbotService depends on FAQ, Student, Course, Enrollment
- Views depend on Models và Services
- Models depend on Django ORM

================================================================================
2. BIỂU ĐỒ USE CASE (USE CASE DIAGRAM)
================================================================================

2.1. CÁC TÁC NHÂN (ACTORS)
--------------------------

Primary Actors:
1. Quản trị viên (Administrator)
   - Có quyền truy cập đầy đủ hệ thống
   - Quản lý sinh viên, môn học, người dùng
   - Xem báo cáo và thống kê

2. Sinh viên (Student)
   - Xem thông tin cá nhân
   - Xem môn học đã đăng ký
   - Xem điểm số
   - Sử dụng chatbot

Secondary Actors:
3. Hệ thống PhoBERT (External System)
   - Xử lý ngôn ngữ tự nhiên cho chatbot

4. Hệ thống Email (External System)
   - Gửi thông báo (nếu có)

2.2. CÁC USE CASE CHÍNH
-----------------------

UC001: Đăng nhập hệ thống
- Actor: Quản trị viên, Sinh viên
- Mô tả: Người dùng đăng nhập vào hệ thống bằng username/password
- Precondition: Có tài khoản hợp lệ
- Postcondition: Được chuyển đến dashboard tương ứng

UC002: Quản lý thông tin sinh viên
- Actor: Quản trị viên
- Mô tả: Thêm, sửa, xóa, tìm kiếm thông tin sinh viên
- Include: UC001 (Đăng nhập)
- Precondition: Đã đăng nhập với quyền admin

UC003: Quản lý môn học
- Actor: Quản trị viên
- Mô tả: Thêm, sửa, xóa, tìm kiếm môn học
- Include: UC001 (Đăng nhập)

UC004: Quản lý đăng ký môn học
- Actor: Quản trị viên
- Mô tả: Đăng ký sinh viên vào môn học, cập nhật điểm
- Include: UC001 (Đăng nhập)

UC005: Xem thông tin cá nhân
- Actor: Sinh viên
- Mô tả: Sinh viên xem thông tin cá nhân của mình
- Include: UC001 (Đăng nhập)

UC006: Xem môn học đã đăng ký
- Actor: Sinh viên
- Mô tả: Sinh viên xem danh sách môn học đã đăng ký và điểm số
- Include: UC001 (Đăng nhập)

UC007: Sử dụng chatbot
- Actor: Quản trị viên, Sinh viên
- Mô tả: Hỏi đáp với chatbot về thông tin hệ thống
- Include: UC001 (Đăng nhập)
- Extend: UC008 (Phân tích ngôn ngữ tự nhiên)

UC008: Phân tích ngôn ngữ tự nhiên
- Actor: Hệ thống PhoBERT
- Mô tả: Phân tích ý định và trả lời câu hỏi của người dùng

UC009: Quản lý FAQ
- Actor: Quản trị viên
- Mô tả: Thêm, sửa, xóa câu hỏi thường gặp cho chatbot
- Include: UC001 (Đăng nhập)

UC010: Xem báo cáo thống kê
- Actor: Quản trị viên
- Mô tả: Xem các báo cáo về sinh viên, môn học, điểm số
- Include: UC001 (Đăng nhập)

2.3. QUAN HỆ GIỮA CÁC USE CASE
------------------------------

Include relationships:
- Tất cả UC002-UC010 đều include UC001 (Đăng nhập)

Extend relationships:
- UC008 extends UC007 (Chatbot sử dụng NLP)

Generalization:
- UC002, UC003, UC004, UC009, UC010 là các chức năng của "Quản lý hệ thống"
- UC005, UC006 là các chức năng của "Xem thông tin"

================================================================================
3. BIỂU ĐỒ HOẠT ĐỘNG (ACTIVITY DIAGRAM)
================================================================================

3.1. ACTIVITY: ĐĂNG NHẬP HỆ THỐNG
----------------------------------

Bắt đầu
  ↓
[Hiển thị form đăng nhập]
  ↓
[Người dùng nhập username/password]
  ↓
<Kiểm tra thông tin đăng nhập>
  ↓ (Hợp lệ)                    ↓ (Không hợp lệ)
[Tạo session]                   [Hiển thị lỗi]
  ↓                               ↓
<Kiểm tra quyền>                [Quay lại form đăng nhập]
  ↓ (Admin)        ↓ (Student)    ↑
[Dashboard Admin]  [Dashboard SV] ←┘
  ↓                ↓
Kết thúc         Kết thúc

3.2. ACTIVITY: QUẢN LÝ SINH VIÊN
---------------------------------

Bắt đầu
  ↓
[Kiểm tra quyền admin]
  ↓ (Có quyền)                   ↓ (Không có quyền)
[Hiển thị danh sách sinh viên]   [Hiển thị lỗi 403]
  ↓                               ↓
<Chọn hành động>                Kết thúc
  ↓ (Thêm)    ↓ (Sửa)    ↓ (Xóa)    ↓ (Tìm kiếm)
[Form thêm]  [Form sửa]  [Xác nhận]  [Nhập từ khóa]
  ↓           ↓          ↓           ↓
[Validate]   [Validate]  [Xóa DB]    [Tìm kiếm DB]
  ↓           ↓          ↓           ↓
<Hợp lệ?>   <Hợp lệ?>   [Thông báo] [Hiển thị kết quả]
  ↓ (Có)      ↓ (Có)     ↓           ↓
[Lưu DB]    [Cập nhật]  ←─────────────┘
  ↓           ↓          ↓
[Thông báo] [Thông báo] [Quay lại danh sách]
  ↓           ↓          ↓
  └─────────────────────┘
  ↓
Kết thúc

3.3. ACTIVITY: SỬ DỤNG CHATBOT
-------------------------------

Bắt đầu
  ↓
[Người dùng nhập tin nhắn]
  ↓
[Gửi tin nhắn đến server]
  ↓
[ChatbotService.process_message()]
  ↓
[Phân tích ý định (analyze_intent)]
  ↓
<Loại ý định>
  ↓ (Thông tin SV)    ↓ (Thông tin môn học)    ↓ (FAQ)    ↓ (Khác)
[get_student_info]   [get_course_info]        [search_faq] [Trả lời mặc định]
  ↓                   ↓                        ↓           ↓
[Truy vấn DB SV]     [Truy vấn DB Course]     [Tìm FAQ]   [Câu trả lời chung]
  ↓                   ↓                        ↓           ↓
[Format kết quả]     [Format kết quả]         [Format]    ←─────────┘
  ↓                   ↓                        ↓
  └─────────────────────────────────────────────┘
  ↓
[Lưu lịch sử chat]
  ↓
[Trả về JSON response]
  ↓
[Hiển thị trên giao diện]
  ↓
Kết thúc

3.4. ACTIVITY: ĐĂNG KÝ MÔN HỌC
-------------------------------

Bắt đầu
  ↓
[Admin chọn sinh viên]
  ↓
[Admin chọn môn học]
  ↓
<Kiểm tra điều kiện>
  ↓ (SV đã đăng ký?)
[Hiển thị lỗi: Đã đăng ký] → Kết thúc
  ↓ (Môn học còn mở?)
[Hiển thị lỗi: Môn đã đóng] → Kết thúc
  ↓ (Hợp lệ)
[Tạo Enrollment record]
  ↓
[Lưu vào database]
  ↓
[Hiển thị thông báo thành công]
  ↓
[Cập nhật danh sách đăng ký]
  ↓
Kết thúc

================================================================================
4. BIỂU ĐỒ TRÌNH TỰ (SEQUENCE DIAGRAM)
================================================================================

4.1. SEQUENCE: ĐĂNG NHẬP HỆ THỐNG
----------------------------------

User → LoginView: POST /login (username, password)
LoginView → Django Auth: authenticate(username, password)
Django Auth → Database: SELECT * FROM auth_user WHERE username=?
Database → Django Auth: User object (if found)
Django Auth → LoginView: User object (if valid)
LoginView → Django Session: create_session(user)
Django Session → Database: INSERT INTO django_session
Database → Django Session: session_key
Django Session → LoginView: session_key
LoginView → User: Redirect to dashboard

4.2. SEQUENCE: SỬ DỤNG CHATBOT
-------------------------------

User → ChatView: POST /chatbot/message {"message": "Thông tin sinh viên SV001"}
ChatView → ChatbotService: process_message(user, message)
ChatbotService → ChatbotService: analyze_intent(message)
ChatbotService → ChatbotService: get_student_info(user, message)
ChatbotService → Student Model: objects.filter(student_id="SV001")
Student Model → Database: SELECT * FROM students_student WHERE student_id=?
Database → Student Model: Student object
Student Model → ChatbotService: Student data
ChatbotService → Enrollment Model: objects.filter(student=student)
Enrollment Model → Database: SELECT * FROM courses_enrollment WHERE student_id=?
Database → Enrollment Model: Enrollment list
Enrollment Model → ChatbotService: Enrollment data
ChatbotService → ChatSession: get_or_create(user=user)
ChatSession → Database: INSERT/SELECT chat session
Database → ChatSession: Session object
ChatSession → ChatbotService: Session object
ChatbotService → ChatHistory: create(session, message_type="user", message)
ChatHistory → Database: INSERT INTO chatbot_chathistory
Database → ChatHistory: History ID
ChatHistory → ChatbotService: Success
ChatbotService → ChatHistory: create(session, message_type="bot", response)
ChatHistory → Database: INSERT INTO chatbot_chathistory
Database → ChatHistory: History ID
ChatHistory → ChatbotService: Success
ChatbotService → ChatView: {"success": True, "response": "Thông tin sinh viên..."}
ChatView → User: JSON response

4.3. SEQUENCE: THÊM SINH VIÊN MỚI
---------------------------------

Admin → StudentCreateView: GET /students/create/
StudentCreateView → AdminRequiredMixin: check_permissions(user)
AdminRequiredMixin → User: is_staff check
User → AdminRequiredMixin: True
AdminRequiredMixin → StudentCreateView: Permission granted
StudentCreateView → Admin: Render student_form.html

Admin → StudentCreateView: POST /students/create/ (form data)
StudentCreateView → StudentForm: is_valid()
StudentForm → StudentForm: validate_student_id()
StudentForm → StudentForm: validate_email()
StudentForm → StudentCreateView: True (if valid)
StudentCreateView → Student Model: create(**validated_data)
Student Model → Database: INSERT INTO students_student
Database → Student Model: Student ID
Student Model → StudentCreateView: Student object
StudentCreateView → Messages: add_message("Sinh viên đã được tạo thành công!")
Messages → StudentCreateView: Success
StudentCreateView → Admin: Redirect to student list

================================================================================
5. BIỂU ĐỒ TRẠNG THÁI (STATE DIAGRAM)
================================================================================

5.1. STATE: TRẠNG THÁI SINH VIÊN
---------------------------------

[Chưa tạo] → (Tạo mới) → [Đang học (is_active=True)]
                              ↓ (Tạm nghỉ)
                         [Tạm nghỉ (is_active=False)]
                              ↓ (Quay lại học)
                         [Đang học (is_active=True)]
                              ↓ (Tốt nghiệp/Thôi học)
                         [Đã kết thúc (is_active=False)]

5.2. STATE: TRẠNG THÁI MÔN HỌC
------------------------------

[Chưa tạo] → (Tạo mới) → [Đang mở (is_active=True)]
                              ↓ (Đóng đăng ký)
                         [Đã đóng (is_active=False)]
                              ↓ (Mở lại)
                         [Đang mở (is_active=True)]

5.3. STATE: TRẠNG THÁI ĐĂNG KÝ MÔN HỌC
---------------------------------------

[Chưa đăng ký] → (Đăng ký) → [Đã đăng ký (grade=null)]
                                   ↓ (Nhập điểm)
                              [Đã có điểm (grade!=null)]
                                   ↓ (Sửa điểm)
                              [Đã có điểm (grade!=null)]

5.4. STATE: TRẠNG THÁI PHIÊN CHAT
----------------------------------

[Chưa tạo] → (Bắt đầu chat) → [Đang hoạt động (is_active=True)]
                                    ↓ (Timeout/Đóng)
                              [Đã kết thúc (is_active=False)]

================================================================================
6. BIỂU ĐỒ THÀNH PHẦN (COMPONENT DIAGRAM)
================================================================================

6.1. CẤU TRÚC TỔNG QUAN
-----------------------

┌─────────────────────────────────────────────────────────────┐
│                    HỆ THỐNG QUẢN LÝ SINH VIÊN               │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Web UI    │  │   Admin UI  │  │  Chatbot UI │         │
│  │ (Bootstrap) │  │  (Django)   │  │ (AJAX/JS)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Students    │  │  Courses    │  │  Chatbot    │         │
│  │   Views     │  │   Views     │  │   Views     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Students    │  │  Courses    │  │  Chatbot    │         │
│  │  Models     │  │  Models     │  │  Service    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Django    │  │  Database   │  │  PhoBERT    │         │
│  │    ORM      │  │   SQLite    │  │   (NLP)     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘

6.2. CHI TIẾT CÁC COMPONENT
---------------------------

Component: Students App
- Interfaces: StudentListView, StudentDetailView, StudentCreateView
- Dependencies: Django ORM, Student Model
- Responsibilities: Quản lý CRUD sinh viên

Component: Courses App  
- Interfaces: CourseListView, EnrollmentView
- Dependencies: Django ORM, Course Model, Enrollment Model
- Responsibilities: Quản lý môn học và đăng ký

Component: Chatbot App
- Interfaces: ChatView, ChatAPI
- Dependencies: ChatbotService, PhoBERT, FAQ Model
- Responsibilities: Xử lý chat và NLP

Component: Authentication
- Interfaces: LoginView, LogoutView
- Dependencies: Django Auth, User Model
- Responsibilities: Xác thực và phân quyền

================================================================================
7. BIỂU ĐỒ TRIỂN KHAI (DEPLOYMENT DIAGRAM)
================================================================================

7.1. KIẾN TRÚC TRIỂN KHAI
-------------------------

┌─────────────────────────────────────────────────────────────┐
│                        CLIENT TIER                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Desktop   │  │   Mobile    │  │   Tablet    │         │
│  │   Browser   │  │   Browser   │  │   Browser   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │ HTTPS
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      WEB SERVER TIER                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 NGINX (Reverse Proxy)                  │ │
│  │  - Static Files Serving                                │ │
│  │  - Load Balancing                                      │ │
│  │  - SSL Termination                                     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   APPLICATION SERVER TIER                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              DJANGO APPLICATION SERVER                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │ Students    │  │  Courses    │  │  Chatbot    │     │ │
│  │  │    App      │  │    App      │  │    App      │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │            CHATBOT SERVICE                          │ │ │
│  │  │  - PhoBERT Integration                              │ │ │
│  │  │  - NLP Processing                                   │ │ │
│  │  │  - Intent Analysis                                  │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ SQL
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      DATABASE TIER                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   SQLite Database                      │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │  Students   │  │   Courses   │  │   Chatbot   │     │ │
│  │  │   Tables    │  │   Tables    │  │   Tables    │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  │  ┌─────────────┐  ┌─────────────┐                      │ │
│  │  │    Auth     │  │   Django    │                      │ │
│  │  │   Tables    │  │   Tables    │                      │ │
│  │  └─────────────┘  └─────────────┘                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

7.2. TRIỂN KHAI PRODUCTION
--------------------------

Production Environment:
┌─────────────────────────────────────────────────────────────┐
│                      LOAD BALANCER                          │
│                        (NGINX)                              │
└─────────────────────────────────────────────────────────────┘
                              │
                    ┌─────────┼─────────┐
                    ▼         ▼         ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│   APP       │ │   APP       │ │   APP       │
│ SERVER 1    │ │ SERVER 2    │ │ SERVER 3    │
│ (Django +   │ │ (Django +   │ │ (Django +   │
│  Gunicorn)  │ │  Gunicorn)  │ │  Gunicorn)  │
└─────────────┘ └─────────────┘ └─────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   DATABASE CLUSTER                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Primary    │  │  Replica 1  │  │  Replica 2  │         │
│  │ PostgreSQL  │  │ PostgreSQL  │  │ PostgreSQL  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘

================================================================================
8. BIỂU ĐỒ GIAO TIẾP (COMMUNICATION DIAGRAM)
================================================================================

8.1. GIAO TIẾP TRONG QUÁ TRÌNH ĐĂNG NHẬP
-----------------------------------------

1: login_request(username, password)
User ────────────────────────────────────→ LoginView
                                              │
2: authenticate(username, password)           │
                ┌─────────────────────────────┘
                ▼
            AuthSystem ←──────────────────── Database
                │    3: query_user(username)      │
                │                                 │
                │ 4: user_data                    │
                └─────────────────────────────────┘
                │
5: create_session(user)
                │
                ▼
            SessionManager ←─────────────────── Database
                │         6: save_session         │
                │                                 │
                │ 7: session_key                  │
                └─────────────────────────────────┘
                │
8: redirect_to_dashboard
                │
                ▼
User ←──────────────────────────────────── LoginView

8.2. GIAO TIẾP TRONG CHATBOT
-----------------------------

1: send_message("Thông tin sinh viên SV001")
User ────────────────────────────────────→ ChatView
                                              │
2: process_message(user, message)             │
                ┌─────────────────────────────┘
                ▼
            ChatbotService
                │ 3: analyze_intent(message)
                │ 4: get_student_info(query)
                ▼
            StudentModel ←───────────────────── Database
                │        5: query_student         │
                │                                 │
                │ 6: student_data                 │
                └─────────────────────────────────┘
                │
7: save_chat_history
                │
                ▼
            ChatHistory ←────────────────────── Database
                │        8: save_message          │
                │                                 │
                │ 9: success                      │
                └─────────────────────────────────┘
                │
10: response_data
                │
                ▼
User ←──────────────────────────────────── ChatView

================================================================================
9. BIỂU ĐỒ PACKAGE (PACKAGE DIAGRAM)
================================================================================

9.1. CẤU TRÚC PACKAGE TỔNG QUAN
-------------------------------

┌─────────────────────────────────────────────────────────────┐
│                    QuanLySinhVien                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  students   │  │   courses   │  │   chatbot   │         │
│  │             │  │             │  │             │         │
│  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │         │
│  │ │ models  │ │  │ │ models  │ │  │ │ models  │ │         │
│  │ │ views   │ │  │ │ views   │ │  │ │ views   │ │         │
│  │ │ urls    │ │  │ │ urls    │ │  │ │ services│ │         │
│  │ │ admin   │ │  │ │ admin   │ │  │ │ urls    │ │         │
│  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                quanlysinhvien (Core)                    │ │
│  │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │ │
│  │  │settings │ │  urls   │ │  views  │ │  forms  │       │ │
│  │  │  wsgi   │ │  asgi   │ │ mixins  │ │decorators│      │ │
│  │  └─────────┘ └─────────┘ └─────────┘ └─────────┘       │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   External Libraries                    │ │
│  │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │ │
│  │  │ Django  │ │Bootstrap│ │ PhoBERT │ │ SQLite  │       │ │
│  │  │  ORM    │ │   CSS   │ │   NLP   │ │Database │       │ │
│  │  └─────────┘ └─────────┘ └─────────┘ └─────────┘       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

9.2. DEPENDENCIES GIỮA CÁC PACKAGE
-----------------------------------

students package:
- Depends on: Django ORM, quanlysinhvien.mixins
- Used by: courses (Enrollment model), chatbot (Student info)

courses package:
- Depends on: Django ORM, students.models, quanlysinhvien.mixins
- Used by: chatbot (Course info)

chatbot package:
- Depends on: Django ORM, students.models, courses.models, PhoBERT
- Used by: All users through chat interface

quanlysinhvien (core):
- Depends on: Django framework
- Used by: All other packages

================================================================================
10. BIỂU ĐỒ TIMING (TIMING DIAGRAM)
================================================================================

10.1. TIMING: XỬ LÝ REQUEST CHATBOT
------------------------------------

Time →  0ms    100ms   200ms   300ms   400ms   500ms   600ms
        │       │       │       │       │       │       │
User    │ Send  │       │       │       │       │ Recv  │
        │ Msg   │       │       │       │       │ Resp  │
        ├───────┼───────┼───────┼───────┼───────┼───────┤
        │       │       │       │       │       │       │
ChatView│       │ Recv  │ Call  │       │       │ Send  │
        │       │ Req   │ Svc   │       │       │ Resp  │
        ├───────┼───────┼───────┼───────┼───────┼───────┤
        │       │       │       │       │       │       │
Service │       │       │ Proc  │ Query │ Format│       │
        │       │       │ Msg   │ DB    │ Resp  │       │
        ├───────┼───────┼───────┼───────┼───────┼───────┤
        │       │       │       │       │       │       │
Database│       │       │       │ Exec  │ Return│       │
        │       │       │       │ Query │ Data  │       │
        └───────┴───────┴───────┴───────┴───────┴───────┘

10.2. TIMING: ĐĂNG NHẬP HỆ THỐNG
---------------------------------

Time →  0ms    200ms   400ms   600ms   800ms   1000ms
        │       │       │       │       │       │
User    │ Login │       │       │       │ Access│
        │ Form  │       │       │       │ Dash  │
        ├───────┼───────┼───────┼───────┼───────┤
        │       │       │       │       │       │
LoginView│      │ Valid │ Auth  │ Session│      │
        │       │ Form  │ User  │ Create │       │
        ├───────┼───────┼───────┼───────┼───────┤
        │       │       │       │       │       │
AuthSys │       │       │ Check │       │       │
        │       │       │ Creds │       │       │
        ├───────┼───────┼───────┼───────┼───────┤
        │       │       │       │       │       │
Database│       │       │ Query │ Save  │       │
        │       │       │ User  │ Sess  │       │
        └───────┴───────┴───────┴───────┴───────┘

================================================================================
11. BIỂU ĐỒ OBJECT (OBJECT DIAGRAM)
================================================================================

11.1. OBJECT: TRẠNG THÁI HỆ THỐNG RUNTIME
------------------------------------------

student1: Student
├─ id = 1
├─ student_id = "SV001"
├─ first_name = "Văn A"
├─ last_name = "Nguyễn"
├─ email = "<EMAIL>"
├─ is_active = True
└─ enrollments = [enrollment1, enrollment2]

course1: Course
├─ id = 1
├─ course_code = "IT001"
├─ name = "Lập trình căn bản"
├─ credits = 3
├─ semester = "1"
├─ year = 2024
├─ is_active = True
└─ enrollments = [enrollment1, enrollment3]

enrollment1: Enrollment
├─ id = 1
├─ student = student1
├─ course = course1
├─ grade = "A"
├─ numeric_grade = 8.5
└─ enrollment_date = "2024-09-01"

user1: User
├─ id = 2
├─ username = "sv001"
├─ email = "<EMAIL>"
├─ is_staff = False
├─ is_active = True
└─ chat_sessions = [session1]

session1: ChatSession
├─ id = 1
├─ user = user1
├─ session_id = "abc123def456"
├─ is_active = True
└─ messages = [history1, history2]

history1: ChatHistory
├─ id = 1
├─ session = session1
├─ message_type = "user"
├─ message = "Thông tin sinh viên SV001"
├─ intent = "student_info"
└─ confidence = 0.95

history2: ChatHistory
├─ id = 2
├─ session = session1
├─ message_type = "bot"
├─ message = "Thông tin sinh viên SV001: Nguyễn Văn A..."
├─ intent = null
└─ response_time = 0.25

faq1: FAQ
├─ id = 1
├─ question = "Làm thế nào để đăng ký môn học?"
├─ answer = "Để đăng ký môn học, bạn cần..."
├─ category = "enrollment"
├─ keywords = "đăng ký, môn học"
└─ is_active = True

11.2. OBJECT: QUAN HỆ GIỮA CÁC OBJECT
--------------------------------------

Relationships:
- student1 ←→ enrollment1 ←→ course1 (Many-to-Many through Enrollment)
- user1 → session1 → [history1, history2] (One-to-Many chains)
- student1 có thể liên kết với user1 qua email matching
- ChatbotService sử dụng faq1 để trả lời câu hỏi

================================================================================
KẾT LUẬN
================================================================================

Hệ thống Quản lý Sinh viên được thiết kế theo kiến trúc MVC với Django framework,
tích hợp chatbot sử dụng PhoBERT cho xử lý tiếng Việt. Các biểu đồ UML trên mô tả
đầy đủ cấu trúc, hành vi và tương tác của hệ thống từ nhiều góc độ khác nhau:

KIẾN TRÚC VÀ THIẾT KẾ:
- Kiến trúc phân lớp rõ ràng (Presentation, Business, Data)
- Tích hợp AI/NLP cho chatbot với PhoBERT
- Bảo mật với Django authentication và authorization
- Thiết kế database tối ưu với các ràng buộc phù hợp
- Giao diện responsive với Bootstrap

CÁC BIỂU ĐỒ UML CHÍNH:
1. Class Diagram: Mô tả cấu trúc lớp và quan hệ
2. Use Case Diagram: Định nghĩa chức năng và tác nhân
3. Activity Diagram: Mô tả quy trình nghiệp vụ
4. Sequence Diagram: Tương tác theo thời gian
5. State Diagram: Trạng thái của các đối tượng
6. Component Diagram: Cấu trúc thành phần hệ thống
7. Deployment Diagram: Kiến trúc triển khai
8. Communication Diagram: Giao tiếp giữa các đối tượng
9. Package Diagram: Tổ chức package và dependencies
10. Timing Diagram: Thời gian xử lý các tác vụ
11. Object Diagram: Trạng thái runtime của hệ thống

ĐIỂM MẠNH CỦA THIẾT KẾ:
- Modular design với Django apps
- Scalable architecture
- AI-powered chatbot
- Comprehensive data model
- Security-first approach
- Performance optimization với indexes
- Maintainable codebase structure

Tài liệu này cung cấp blueprint đầy đủ cho việc phát triển, bảo trì và mở rộng
hệ thống Quản lý Sinh viên, đảm bảo tính nhất quán và chất lượng code.

================================================================================
