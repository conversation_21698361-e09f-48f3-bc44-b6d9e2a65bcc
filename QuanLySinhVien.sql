-- =====================================================
-- HỆ THỐNG QUẢN LÝ SINH VIÊN - CSDL EXPORT
-- Ngày tạo: 2025-07-16
-- <PERSON><PERSON> tả: File SQL export từ hệ thống quản lý sinh viên Django
-- =====================================================

-- Tắt foreign key checks để import dễ dàng hơn
PRAGMA foreign_keys = OFF;

-- =====================================================
-- 1. BẢNG AUTH_USER (Django User Model)
-- =====================================================
CREATE TABLE IF NOT EXISTS "auth_user" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "password" varchar(128) NOT NULL,
    "last_login" datetime NULL,
    "is_superuser" bool NOT NULL,
    "username" varchar(150) NOT NULL UNIQUE,
    "first_name" varchar(150) NOT NULL,
    "last_name" varchar(150) NOT NULL,
    "email" varchar(254) NOT NULL,
    "is_staff" bool NOT NULL,
    "is_active" bool NOT NULL,
    "date_joined" datetime NOT NULL
);

-- =====================================================
-- 2. BẢNG AUTH_GROUP (Django Group Model)
-- =====================================================
CREATE TABLE IF NOT EXISTS "auth_group" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" varchar(150) NOT NULL UNIQUE
);

-- =====================================================
-- 3. BẢNG AUTH_USER_GROUPS (Many-to-Many)
-- =====================================================
CREATE TABLE IF NOT EXISTS "auth_user_groups" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "group_id" integer NOT NULL REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED
);

-- =====================================================
-- 4. BẢNG STUDENTS_STUDENT (Quản lý sinh viên)
-- =====================================================
CREATE TABLE IF NOT EXISTS "students_student" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "student_id" varchar(20) NOT NULL UNIQUE,
    "first_name" varchar(50) NOT NULL,
    "last_name" varchar(50) NOT NULL,
    "date_of_birth" date NOT NULL,
    "gender" varchar(1) NOT NULL,
    "email" varchar(254) NOT NULL UNIQUE,
    "phone_number" varchar(15) NOT NULL,
    "address" text NULL,
    "enrollment_date" date NOT NULL,
    "photo" varchar(100) NULL,
    "is_active" bool NOT NULL DEFAULT 1
);

-- =====================================================
-- 5. BẢNG COURSES_COURSE (Quản lý môn học)
-- =====================================================
CREATE TABLE IF NOT EXISTS "courses_course" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "course_code" varchar(10) NOT NULL UNIQUE,
    "name" varchar(100) NOT NULL,
    "description" text NULL,
    "credits" smallint unsigned NOT NULL CHECK ("credits" >= 0),
    "semester" varchar(1) NOT NULL,
    "year" smallint unsigned NOT NULL CHECK ("year" >= 0),
    "start_date" date NOT NULL,
    "end_date" date NOT NULL,
    "is_active" bool NOT NULL DEFAULT 1
);

-- =====================================================
-- 6. BẢNG COURSES_ENROLLMENT (Đăng ký môn học)
-- =====================================================
CREATE TABLE IF NOT EXISTS "courses_enrollment" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "enrollment_date" date NOT NULL,
    "grade" varchar(2) NULL,
    "numeric_grade" decimal(4,2) NULL,
    "course_id" bigint NOT NULL REFERENCES "courses_course" ("id") DEFERRABLE INITIALLY DEFERRED,
    "student_id" bigint NOT NULL REFERENCES "students_student" ("id") DEFERRABLE INITIALLY DEFERRED
);

-- =====================================================
-- 7. BẢNG CHATBOT_FAQ (Câu hỏi thường gặp)
-- =====================================================
CREATE TABLE IF NOT EXISTS "chatbot_faq" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "question" text NOT NULL,
    "answer" text NOT NULL,
    "category" varchar(20) NOT NULL DEFAULT 'general',
    "keywords" text NULL,
    "is_active" bool NOT NULL DEFAULT 1,
    "created_at" datetime NOT NULL,
    "updated_at" datetime NOT NULL
);

-- =====================================================
-- 8. BẢNG CHATBOT_CHATSESSION (Phiên chat)
-- =====================================================
CREATE TABLE IF NOT EXISTS "chatbot_chatsession" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "session_id" varchar(100) NOT NULL UNIQUE,
    "started_at" datetime NOT NULL,
    "last_activity" datetime NOT NULL,
    "is_active" bool NOT NULL DEFAULT 1,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);

-- =====================================================
-- 9. BẢNG CHATBOT_CHATHISTORY (Lịch sử chat)
-- =====================================================
CREATE TABLE IF NOT EXISTS "chatbot_chathistory" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "message_type" varchar(10) NOT NULL,
    "message" text NOT NULL,
    "intent" varchar(50) NULL,
    "confidence" real NULL,
    "response_time" real NULL,
    "timestamp" datetime NOT NULL,
    "session_id" bigint NOT NULL REFERENCES "chatbot_chatsession" ("id") DEFERRABLE INITIALLY DEFERRED
);

-- =====================================================
-- TẠO CÁC INDEX
-- =====================================================

-- Index cho bảng students_student
CREATE INDEX IF NOT EXISTS "students_student_student_id_idx" ON "students_student" ("student_id");
CREATE INDEX IF NOT EXISTS "students_student_email_idx" ON "students_student" ("email");
CREATE INDEX IF NOT EXISTS "students_student_is_active_idx" ON "students_student" ("is_active");

-- Index cho bảng courses_course
CREATE INDEX IF NOT EXISTS "courses_course_course_code_idx" ON "courses_course" ("course_code");
CREATE INDEX IF NOT EXISTS "courses_course_semester_year_idx" ON "courses_course" ("semester", "year");
CREATE INDEX IF NOT EXISTS "courses_course_is_active_idx" ON "courses_course" ("is_active");

-- Index cho bảng courses_enrollment
CREATE UNIQUE INDEX IF NOT EXISTS "courses_enrollment_student_id_course_id_uniq" ON "courses_enrollment" ("student_id", "course_id");
CREATE INDEX IF NOT EXISTS "courses_enrollment_course_id_idx" ON "courses_enrollment" ("course_id");
CREATE INDEX IF NOT EXISTS "courses_enrollment_student_id_idx" ON "courses_enrollment" ("student_id");

-- Index cho bảng chatbot
CREATE INDEX IF NOT EXISTS "chatbot_chatsession_user_id_idx" ON "chatbot_chatsession" ("user_id");
CREATE INDEX IF NOT EXISTS "chatbot_chatsession_session_id_idx" ON "chatbot_chatsession" ("session_id");
CREATE INDEX IF NOT EXISTS "chatbot_chathistory_session_id_idx" ON "chatbot_chathistory" ("session_id");
CREATE INDEX IF NOT EXISTS "chatbot_faq_category_idx" ON "chatbot_faq" ("category");
CREATE INDEX IF NOT EXISTS "chatbot_faq_is_active_idx" ON "chatbot_faq" ("is_active");

-- Index cho auth tables
CREATE INDEX IF NOT EXISTS "auth_user_groups_user_id_idx" ON "auth_user_groups" ("user_id");
CREATE INDEX IF NOT EXISTS "auth_user_groups_group_id_idx" ON "auth_user_groups" ("group_id");

-- =====================================================
-- DỮ LIỆU MẪU
-- =====================================================

-- Thêm groups mặc định
INSERT OR IGNORE INTO "auth_group" ("id", "name") VALUES 
(1, 'Administrators'),
(2, 'Students');

-- Thêm user admin mặc định (password: admin123)
INSERT OR IGNORE INTO "auth_user" ("id", "password", "last_login", "is_superuser", "username", "first_name", "last_name", "email", "is_staff", "is_active", "date_joined") VALUES 
(1, 'pbkdf2_sha256$600000$admin123$hash', NULL, 1, 'admin', 'Admin', 'System', '<EMAIL>', 1, 1, datetime('now'));

-- Gán admin vào group Administrators
INSERT OR IGNORE INTO "auth_user_groups" ("user_id", "group_id") VALUES (1, 1);

-- Thêm dữ liệu sinh viên mẫu
INSERT OR IGNORE INTO "students_student" ("id", "student_id", "first_name", "last_name", "date_of_birth", "gender", "email", "phone_number", "address", "enrollment_date", "photo", "is_active") VALUES 
(1, 'SV001', 'Văn A', 'Nguyễn', '2003-01-15', 'M', '<EMAIL>', '0901234567', '123 Đường ABC, Quận 1, TP.HCM', '2021-09-01', NULL, 1),
(2, 'SV002', 'Thị B', 'Trần', '2003-03-20', 'F', '<EMAIL>', '0902345678', '456 Đường DEF, Quận 2, TP.HCM', '2021-09-01', NULL, 1),
(3, 'SV003', 'Văn C', 'Lê', '2003-05-10', 'M', '<EMAIL>', '0903456789', '789 Đường GHI, Quận 3, TP.HCM', '2021-09-01', NULL, 1);

-- Thêm dữ liệu môn học mẫu
INSERT OR IGNORE INTO "courses_course" ("id", "course_code", "name", "description", "credits", "semester", "year", "start_date", "end_date", "is_active") VALUES 
(1, 'IT001', 'Lập trình căn bản', 'Môn học cơ bản về lập trình', 3, '1', 2024, '2024-09-01', '2024-12-15', 1),
(2, 'IT002', 'Cấu trúc dữ liệu', 'Môn học về cấu trúc dữ liệu và giải thuật', 4, '1', 2024, '2024-09-01', '2024-12-15', 1),
(3, 'IT003', 'Cơ sở dữ liệu', 'Môn học về thiết kế và quản lý cơ sở dữ liệu', 3, '2', 2024, '2025-01-15', '2025-05-15', 1);

-- Thêm dữ liệu đăng ký môn học mẫu
INSERT OR IGNORE INTO "courses_enrollment" ("id", "enrollment_date", "grade", "numeric_grade", "course_id", "student_id") VALUES 
(1, '2024-09-01', 'A', 8.5, 1, 1),
(2, '2024-09-01', 'B+', 7.8, 2, 1),
(3, '2024-09-01', 'A', 8.2, 1, 2),
(4, '2024-09-01', 'B', 7.0, 2, 2),
(5, '2024-09-01', 'A+', 9.0, 1, 3);

-- Thêm dữ liệu FAQ mẫu cho chatbot
INSERT OR IGNORE INTO "chatbot_faq" ("id", "question", "answer", "category", "keywords", "is_active", "created_at", "updated_at") VALUES 
(1, 'Làm thế nào để đăng ký môn học?', 'Để đăng ký môn học, bạn cần đăng nhập vào hệ thống và truy cập mục "Đăng ký môn học". Chọn môn học mong muốn và nhấn "Đăng ký".', 'enrollment', 'đăng ký, môn học, đăng ký môn học', 1, datetime('now'), datetime('now')),
(2, 'Làm sao để xem điểm số?', 'Bạn có thể xem điểm số bằng cách đăng nhập và truy cập mục "Kết quả học tập" hoặc "Điểm số" trong menu chính.', 'grade', 'điểm, điểm số, kết quả học tập', 1, datetime('now'), datetime('now')),
(3, 'Thông tin liên hệ phòng đào tạo?', 'Phòng Đào tạo: Tầng 2, Tòa nhà A. Điện thoại: (028) 1234-5678. Email: <EMAIL>. Giờ làm việc: 7:30-11:30, 13:30-17:00 (Thứ 2-6)', 'general', 'liên hệ, phòng đào tạo, thông tin', 1, datetime('now'), datetime('now')),
(4, 'Cách thay đổi thông tin cá nhân?', 'Để thay đổi thông tin cá nhân, vui lòng đăng nhập và truy cập "Hồ sơ cá nhân". Một số thông tin quan trọng cần liên hệ phòng đào tạo để thay đổi.', 'student', 'thông tin cá nhân, hồ sơ, thay đổi', 1, datetime('now'), datetime('now')),
(5, 'Quy định về học phí?', 'Học phí được thu theo từng học kỳ. Sinh viên cần nộp học phí trước khi bắt đầu học kỳ. Thông tin chi tiết về mức học phí vui lòng liên hệ phòng tài chính.', 'general', 'học phí, tài chính, nộp tiền', 1, datetime('now'), datetime('now'));

-- =====================================================
-- CÁC BẢNG DJANGO SYSTEM (Tùy chọn)
-- =====================================================

-- Bảng Django Migrations
CREATE TABLE IF NOT EXISTS "django_migrations" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "app" varchar(255) NOT NULL,
    "name" varchar(255) NOT NULL,
    "applied" datetime NOT NULL
);

-- Bảng Django Content Types
CREATE TABLE IF NOT EXISTS "django_content_type" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "app_label" varchar(100) NOT NULL,
    "model" varchar(100) NOT NULL
);

-- Bảng Django Sessions
CREATE TABLE IF NOT EXISTS "django_session" (
    "session_key" varchar(40) NOT NULL PRIMARY KEY,
    "session_data" text NOT NULL,
    "expire_date" datetime NOT NULL
);

-- Thêm dữ liệu migrations
INSERT OR IGNORE INTO "django_migrations" ("id", "app", "name", "applied") VALUES
(1, 'contenttypes', '0001_initial', datetime('now')),
(2, 'auth', '0001_initial', datetime('now')),
(3, 'students', '0001_initial', datetime('now')),
(4, 'courses', '0001_initial', datetime('now')),
(5, 'chatbot', '0001_initial', datetime('now')),
(6, 'sessions', '0001_initial', datetime('now'));

-- Thêm content types
INSERT OR IGNORE INTO "django_content_type" ("id", "app_label", "model") VALUES
(1, 'auth', 'user'),
(2, 'auth', 'group'),
(3, 'students', 'student'),
(4, 'courses', 'course'),
(5, 'courses', 'enrollment'),
(6, 'chatbot', 'faq'),
(7, 'chatbot', 'chatsession'),
(8, 'chatbot', 'chathistory');

-- =====================================================
-- THÊM DỮ LIỆU MẪU NÂNG CAO
-- =====================================================

-- Thêm user sinh viên mẫu (password: student123)
INSERT OR IGNORE INTO "auth_user" ("id", "password", "last_login", "is_superuser", "username", "first_name", "last_name", "email", "is_staff", "is_active", "date_joined") VALUES
(2, 'pbkdf2_sha256$600000$student123$hash', NULL, 0, 'sv001', 'Văn A', 'Nguyễn', '<EMAIL>', 0, 1, datetime('now')),
(3, 'pbkdf2_sha256$600000$student123$hash', NULL, 0, 'sv002', 'Thị B', 'Trần', '<EMAIL>', 0, 1, datetime('now')),
(4, 'pbkdf2_sha256$600000$student123$hash', NULL, 0, 'sv003', 'Văn C', 'Lê', '<EMAIL>', 0, 1, datetime('now'));

-- Gán sinh viên vào group Students
INSERT OR IGNORE INTO "auth_user_groups" ("user_id", "group_id") VALUES
(2, 2),
(3, 2),
(4, 2);

-- Thêm môn học bổ sung
INSERT OR IGNORE INTO "courses_course" ("id", "course_code", "name", "description", "credits", "semester", "year", "start_date", "end_date", "is_active") VALUES
(4, 'IT004', 'Mạng máy tính', 'Môn học về mạng máy tính và giao thức mạng', 3, '2', 2024, '2025-01-15', '2025-05-15', 1),
(5, 'IT005', 'Hệ điều hành', 'Môn học về hệ điều hành và quản lý tài nguyên', 3, '1', 2025, '2025-09-01', '2025-12-15', 1),
(6, 'MATH001', 'Toán cao cấp A1', 'Môn toán cao cấp phần 1', 4, '1', 2024, '2024-09-01', '2024-12-15', 1);

-- Thêm đăng ký môn học bổ sung
INSERT OR IGNORE INTO "courses_enrollment" ("id", "enrollment_date", "grade", "numeric_grade", "course_id", "student_id") VALUES
(6, '2025-01-15', NULL, NULL, 3, 1),
(7, '2025-01-15', NULL, NULL, 4, 1),
(8, '2024-09-01', 'B+', 7.5, 6, 2),
(9, '2025-01-15', NULL, NULL, 3, 2),
(10, '2024-09-01', 'A', 8.0, 6, 3),
(11, '2025-01-15', NULL, NULL, 3, 3);

-- Thêm FAQ bổ sung
INSERT OR IGNORE INTO "chatbot_faq" ("id", "question", "answer", "category", "keywords", "is_active", "created_at", "updated_at") VALUES
(6, 'Thời khóa biểu học tập như thế nào?', 'Thời khóa biểu được cập nhật hàng tuần trên hệ thống. Sinh viên có thể xem trong mục "Thời khóa biểu" sau khi đăng nhập.', 'course', 'thời khóa biểu, lịch học, lịch', 1, datetime('now'), datetime('now')),
(7, 'Quy định về vắng mặt?', 'Sinh viên không được vắng mặt quá 20% số tiết của môn học. Vắng mặt có phép cần có giấy xin phép hợp lệ.', 'course', 'vắng mặt, nghỉ học, điểm danh', 1, datetime('now'), datetime('now')),
(8, 'Cách đăng ký thi lại?', 'Để đăng ký thi lại, sinh viên cần nộp đơn tại phòng đào tạo trong thời gian quy định và đóng phí thi lại.', 'grade', 'thi lại, đăng ký thi lại, thi', 1, datetime('now'), datetime('now')),
(9, 'Thông tin về học bổng?', 'Thông tin học bổng được thông báo qua website và email sinh viên. Điều kiện xét học bổng dựa trên kết quả học tập và hoàn cảnh gia đình.', 'general', 'học bổng, hỗ trợ, tài chính', 1, datetime('now'), datetime('now')),
(10, 'Làm thế nào để xin chuyển ngành?', 'Để xin chuyển ngành, sinh viên cần có GPA >= 2.5, nộp đơn tại phòng đào tạo và tham gia kỳ thi tuyển chuyển ngành (nếu có).', 'student', 'chuyển ngành, đổi ngành, ngành học', 1, datetime('now'), datetime('now'));

-- =====================================================
-- VIEWS VÀ STORED PROCEDURES (Tùy chọn cho MySQL/PostgreSQL)
-- =====================================================

-- View: Thống kê sinh viên theo khóa học
-- CREATE VIEW IF NOT EXISTS student_course_stats AS
-- SELECT
--     c.course_code,
--     c.name as course_name,
--     COUNT(e.student_id) as total_students,
--     AVG(e.numeric_grade) as average_grade,
--     COUNT(CASE WHEN e.grade IN ('A+', 'A') THEN 1 END) as excellent_count,
--     COUNT(CASE WHEN e.grade = 'F' THEN 1 END) as failed_count
-- FROM courses_course c
-- LEFT JOIN courses_enrollment e ON c.id = e.course_id
-- WHERE c.is_active = 1
-- GROUP BY c.id, c.course_code, c.name;

-- View: Thông tin sinh viên đầy đủ
-- CREATE VIEW IF NOT EXISTS student_full_info AS
-- SELECT
--     s.student_id,
--     s.first_name,
--     s.last_name,
--     s.email,
--     s.phone_number,
--     s.enrollment_date,
--     COUNT(e.course_id) as total_courses,
--     AVG(e.numeric_grade) as gpa,
--     s.is_active
-- FROM students_student s
-- LEFT JOIN courses_enrollment e ON s.id = e.student_id
-- GROUP BY s.id;

-- Bật lại foreign key checks
PRAGMA foreign_keys = ON;

-- =====================================================
-- HƯỚNG DẪN SỬ DỤNG
-- =====================================================
/*
HƯỚNG DẪN IMPORT DATABASE:

1. Để import vào SQLite:
   sqlite3 db.sqlite3 < QuanLySinhVien.sql

2. Để import vào MySQL:
   - Thay đổi các kiểu dữ liệu phù hợp với MySQL
   - Sử dụng: mysql -u username -p database_name < QuanLySinhVien.sql

3. Để import vào PostgreSQL:
   - Thay đổi các kiểu dữ liệu phù hợp với PostgreSQL
   - Sử dụng: psql -U username -d database_name -f QuanLySinhVien.sql

THÔNG TIN ĐĂNG NHẬP MẶC ĐỊNH:
- Admin: username=admin, password=admin123
- Sinh viên: username=sv001/sv002/sv003, password=student123

LƯU Ý:
- File này được tạo cho SQLite, cần điều chỉnh syntax cho các DBMS khác
- Passwords được hash, cần reset lại trong Django admin
- Dữ liệu mẫu chỉ để test, cần thay thế bằng dữ liệu thực tế
*/

-- =====================================================
-- KẾT THÚC FILE SQL
-- Tổng số bảng: 9 bảng chính + các bảng Django system
-- Tổng số records mẫu: ~50 records
-- Phiên bản: 1.0
-- =====================================================
